<style>
/* Стилове за секцията с управление на изображения */
#tab-images .panel {
    margin-bottom: 20px;
}

#tab-images .panel-heading .pull-right {
    margin-top: -5px;
}

#image-stats-content .panel {
    margin-bottom: 0;
    transition: all 0.3s ease;
}

#image-stats-content .panel:hover {
    transform: translateY(-2px);
    box-shadow: 0 4px 8px rgba(0,0,0,0.1);
}

#image-stats-content .panel-heading {
    padding: 15px;
}

#image-stats-content .panel-heading i {
    margin-bottom: 10px;
}

#image-stats-content .panel-body h3 {
    margin: 0;
    font-size: 2.5em;
    font-weight: bold;
}

#button-retry-failed-images {
    font-size: 12px;
    font-weight: bold;
}

.table-condensed th,
.table-condensed td {
    padding: 8px;
}

#cron-info-table *,
#cron-logs-table * 
{
  font-size: 12px;
}

/* Анимация за зареждане */
.fa-spin {
    animation: fa-spin 1s infinite linear;
}

@keyframes fa-spin {
    0% { transform: rotate(0deg); }
    100% { transform: rotate(360deg); }
}

/* Стилове за разширената функционалност */
#suppliers-extended-content {
    border-top: 2px solid #ddd;
    padding-top: 20px;
}

.target-path-edit {
    position: relative;
}

.suggestions-dropdown {
    position: absolute;
    top: 100%;
    left: 0;
    right: 0;
    background: white;
    border: 1px solid #ccc;
    border-top: none;
    max-height: 200px;
    overflow-y: auto;
    z-index: 1000;
    box-shadow: 0 2px 4px rgba(0,0,0,0.1);
}

.suggestion-item {
    padding: 8px 12px;
    cursor: pointer;
    border-bottom: 1px solid #eee;
}

.suggestion-item:hover {
    background-color: #f5f5f5;
}

.suggestion-item:last-child {
    border-bottom: none;
}

#categories-mapping-table tr.warning {
    background-color: #fcf8e3 !important;
}

#categories-mapping-table .label-warning {
    margin-left: 5px;
}

/* Стилове за разширената функционалност */
#suppliers-extended-content {
    border-top: 2px solid #ddd;
    padding-top: 20px;
}

.target-path-edit {
    position: relative;
}

.suggestions-dropdown {
    position: absolute;
    top: 100%;
    left: 0;
    right: 0;
    background: white;
    border: 1px solid #ccc;
    border-top: none;
    max-height: 200px;
    overflow-y: auto;
    z-index: 1000;
    box-shadow: 0 2px 4px rgba(0,0,0,0.1);
}

.suggestion-item {
    padding: 8px 12px;
    cursor: pointer;
    border-bottom: 1px solid #eee;
}

.suggestion-item:hover {
    background-color: #f5f5f5;
}

.suggestion-item:last-child {
    border-bottom: none;
}

#categories-mapping-table tr.warning {
    background-color: #fcf8e3 !important;
}

#categories-mapping-table .label-warning {
    margin-left: 5px;
}

#categories-mapping-table tr.info {
    background-color: #d9edf7 !important;
}

#categories-mapping-table .label-info {
    margin-left: 5px;
}

.new-category-form {
    background-color: #f9f9f9;
    border: 1px solid #ddd;
    border-radius: 4px;
    padding: 10px;
    margin-top: 10px;
}

.new-category-form h5 {
    margin-top: 0;
    color: #337ab7;
}

.parent-suggestions-dropdown {
    position: absolute;
    z-index: 1000;
    background: white;
    border: 1px solid #ccc;
    border-radius: 4px;
    max-height: 200px;
    overflow-y: auto;
    width: 100%;
}

.parent-suggestion-item {
    padding: 8px 12px;
    cursor: pointer;
    border-bottom: 1px solid #eee;
}

.parent-suggestion-item:hover {
    background-color: #f5f5f5;
}

.parent-suggestion-item:last-child {
    border-bottom: none;
}

.create-new-category-btn {
    margin-bottom: 5px;
}

/* Стилове за табове - скриване на неактивните */
.tab-content .tab-pane {
    display: none !important;
    visibility: hidden !important;
    opacity: 0 !important;
    height: 0 !important;
    overflow: hidden !important;
}

.tab-content .tab-pane.active {
    display: block !important;
    visibility: visible !important;
    opacity: 1 !important;
    height: auto !important;
    overflow: visible !important;
}

/* Допълнителни правила за сигурност */
#tab-suppliers:not(.active),
#tab-logs:not(.active),
#tab-images:not(.active),
#tab-dev-logs:not(.active) {
    display: none !important;
    visibility: hidden !important;
    opacity: 0 !important;
    height: 0 !important;
    overflow: hidden !important;
}

#tab-suppliers.active,
#tab-logs.active,
#tab-images.active,
#tab-dev-logs.active {
    display: block !important;
    visibility: visible !important;
    opacity: 1 !important;
    height: auto !important;
    overflow: visible !important;
}

/* Стилове за навигацията на табовете */
.nav-tabs > li {
    position: relative;
}

.nav-tabs > li.active > a,
.nav-tabs > li.active > a:hover,
.nav-tabs > li.active > a:focus {
    color: #555 !important;
    background-color: #fff !important;
    border: 1px solid #ddd !important;
    border-bottom-color: transparent !important;
    cursor: default !important;
}

.nav-tabs > li > a {
    color: #337ab7 !important;
    background-color: transparent !important;
}

.nav-tabs > li > a:hover {
    border-color: #eee #eee #ddd !important;
    background-color: #eee !important;
}
</style>
{{ header }}{{ column_left }}
<div id="content">
  <div class="page-header">
    <div class="container-fluid">
      <div class="pull-right">
        {# <button type="submit" form="form-module" data-toggle="tooltip" title="{{ button_save }}" class="btn btn-primary"><i class="fa fa-save"></i></button> #}
        <a href="{{ cancel }}" data-toggle="tooltip" title="{{ button_cancel }}" class="btn btn-default"><i class="fa fa-reply"></i></a></div>
      <h1>{{ heading_title }}</h1>
      <ul class="breadcrumb">
        {% for breadcrumb in breadcrumbs %}
        <li><a href="{{ breadcrumb.href }}">{{ breadcrumb.text }}</a></li>
        {% endfor %}
      </ul>
    </div>
  </div>
  
  <div class="container-fluid">
    {% if error_warning %}
    <div class="alert alert-danger alert-dismissible"><i class="fa fa-exclamation-circle"></i> {{ error_warning }}
      <button type="button" class="close" data-dismiss="alert">&times;</button>
    </div>
    {% endif %}
    {% if success %}
    <div class="alert alert-success alert-dismissible"><i class="fa fa-check-circle"></i> {{ success }}
      <button type="button" class="close" data-dismiss="alert">&times;</button>
    </div>
    {% endif %}

    <div class="panel panel-default">
      <div class="panel-heading">
        <h3 class="panel-title"><i class="fa fa-pencil"></i> {{ text_edit }}</h3>
      </div>
      <div class="panel-body">
        <ul class="nav nav-tabs">
          <li class="active"><a href="#tab-suppliers" data-toggle="tab">{{ tab_suppliers }}</a></li> {# [cite: 6] #}
          <li><a href="#tab-logs" data-toggle="tab">{{ tab_logs }}</a></li> {# [cite: 6] #}
          <li><a href="#tab-images" data-toggle="tab">Управление на изображения</a></li>
          {# Условно добавяне на таб "Dev логове" #}
          {% if is_developer %}
          <li><a href="#tab-dev-logs" data-toggle="tab">{{ tab_dev_logs }}</a></li>
          {% endif %}
        </ul>
        <div class="tab-content">
          {# Таб "Доставчици" #}
          <div class="tab-pane active" id="tab-suppliers"> {# [cite: 7] #}
            <div class="table-responsive">
              <table class="table table-bordered table-hover">
                <thead>
                  <tr>
                    <td class="text-left">{{ column_supplier }}</td> {# [cite: 8] #}
                    <td class="text-left">{{ column_connection_type }}</td> {# [cite: 8] #}
                    <td class="text-left">{{ column_downloaded_data }}</td>
                    <td class="text-center">{{ column_status }}</td> {# [cite: 8] #}
                    <td class="text-right">{{ column_action }}</td> {# [cite: 8] #}
                  </tr>
                </thead>
                <tbody id="activated-suppliers-list">
                  {% if activated_suppliers %}
                  {% for supplier in activated_suppliers %}
                  <tr id="supplier-row-{{ supplier.mfsc_id }}">
                    <td class="text-left">{{ supplier.name }}</td>
                    <td class="text-left">{{ supplier.connection_type }}</td>
                    <td class="text-left" id="downloaded-data-{{ supplier.mfsc_id }}">
                      {{ supplier.downloaded_data|raw }}
                      {% if supplier.downloaded_data and supplier.downloaded_data != '<span class="text-muted">Няма данни</span>' %}
                      <br><button type="button" class="btn btn-primary btn-xs download-file-again-btn"
                                  data-mfsc-id="{{ supplier.mfsc_id }}"
                                  data-toggle="tooltip" title="{{ button_download_file_again }}"
                                  style="margin-top: 5px;">
                        <i class="fa fa-download"></i> Изтегли отново
                      </button>
                      {% endif %}
                    </td>
                    <td class="text-center" id="status-{{ supplier.mfsc_id }}"><span class="label label-default" title="{{ text_status_unknown }}">?</span></td>
                    <td class="text-right">
                      <button type="button" class="btn btn-info btn-xs check-connection" data-connector-id="{{ supplier.mfsc_id }}" data-toggle="tooltip" title="{{ button_check_connection }}"><i class="fa fa-plug"></i></button> {# [cite: 8] #}
                      <button type="button" class="btn btn-success btn-xs manual-sync-btn"
                            data-mfsc-id="{{ supplier.mfsc_id }}"
                            data-supplier-name="{{ supplier.name }}"
                            data-toggle="tooltip" title="{{ button_manual_sync }}" style="padding: 5px 10; margin-left: 10px;">
                            <i class="fa fa-refresh"></i> {{ button_manual_sync }}
                      </button>
                    </td>
                  </tr>
                  {% endfor %}
                  {% else %}
                  <tr>
                    <td class="text-center" colspan="5">{{ text_no_results }}</td>
                  </tr>
                  {% endif %}
                </tbody>
              </table>
            </div>

            {# Ново съдържание под таблицата с доставчиците #}
            <div id="suppliers-extended-content" style="margin-top: 20px;">
              {# Това съдържание ще се зарежда динамично чрез AJAX #}
            </div>

            
            {% if new_connectors %}
            <h4>{{ text_new_connectors }}</h4> {# [cite: 13] #}
            <div class="table-responsive" >
              <table class="table table-bordered">
                <tbody id="new-connectors-list">
                  {% for new_connector in new_connectors %}
                  <tr id="new-connector-row-{{ new_connector.connector_key }}"> {# [cite: 18] #}
                    <td class="text-left">{{ new_connector.name }}</td> {# [cite: 14] #}
                    <td class="text-right">
                      <button type="button" class="btn btn-success btn-sm activate-connector" data-connector-key="{{ new_connector.connector_key }}"><i class="fa fa-plus-circle"></i> {{ text_activate }}</button> {# [cite: 14, 16] #}
                    </td>
                  </tr>
                  {% endfor %}
                </tbody>
              </table>
            </div>
            {% endif %}
          </div>

          {# Таб "Логове" #}
          <div class="tab-pane" id="tab-logs"> {# [cite: 19] #}
            <h3 style="font-size: 18px; font-weight: bold;">{{ text_logs_title }}</h3> {# [cite: 20] #}
            <div class="table-responsive" style="max-width: 1336px;  overflow-x: auto;">
              <table class="table table-bordered table-hover" id="cron-logs-table">
                <thead>
                  <tr> {# [cite: 22] #}
                    <td class="text-left">{{ column_date }}</td> 
                    <td class="text-left">{{ column_log_supplier }}</td> 
                    <td class="text-center">{{ column_connection_status }}</td> 
                    <td class="text-right">{{ column_duration }}</td> 
                    <td class="text-left" style="width: 25%;">{{ column_information }}</td> 
                  </tr>
                </thead>
                <tbody>
                  {% if logs %}
                  {% for log in logs %}
                  <tr>
                    <td class="text-left">{{ log.date }}</td>
                    <td class="text-left">{{ log.supplier }}</td>
                    <td class="text-center">
                        {% if log.connection_status == text_success_status %}
                            <span class="label label-success">✓</span> {# [cite: 9] #}
                        {% else %}
                             <span class="label label-danger">✕</span> {# [cite: 9] #}
                        {% endif %}
                    </td>
                    <td class="text-right">{{ log.duration }}</td>
                    <td class="text-left">{{ log.information }}</td>
                  </tr>
                  {% endfor %}
                  {% else %}
                  <tr>
                    <td class="text-center" colspan="5">{{ text_no_results }}</td>
                  </tr>
                  {% endif %}
                </tbody>
              </table>
            </div>
            <div class="row"> {# [cite: 21] #}
              <div class="col-sm-6 text-left">{{ pagination }}</div>
              <div class="col-sm-6 text-right">{{ results_count }}</div>
            </div>
          </div>

          {# Таб "Управление на Изображения" #}
          <div class="tab-pane" id="tab-images">
            <h3 style="font-size: 18px; font-weight: bold;">Управление на опашката с изображения</h3>

            {# Секция за статистика #}
            <div class="panel panel-info">
              <div class="panel-heading">
                <h3 class="panel-title" style="width: 100%;">
                  <i class="fa fa-bar-chart"></i> Статистика на опашката
                  <div class="pull-right">
                    <button type="button" id="button-refresh-stats" class="btn btn-info btn-xs" data-toggle="tooltip" title="Обнови статистиката">
                      <i class="fa fa-refresh"></i> Обнови
                    </button>
                  </div>
                </h3>
              </div>
              <div class="panel-body">
                <div id="image-stats-content">
                  <div class="text-center">
                    <i class="fa fa-spinner fa-spin fa-2x"></i>
                    <p>Зареждане на статистика...</p>
                  </div>
                </div>
              </div>
            </div>

            {# Секция за управление на failed изображения #}
            <div class="panel panel-warning">
              <div class="panel-heading">
                <h3 class="panel-title">
                  <i class="fa fa-exclamation-triangle"></i> Управление на неуспешни изображения
                </h3>
              </div>
              <div class="panel-body">
                <div class="row">
                  <div class="col-md-8">
                    <p><strong>Рестартиране на failed изображения:</strong></p>
                    <p class="text-muted">
                      Този бутон ще рестартира всички изображения със статус "failed" и attempts < 5,
                      като ги върне в статус "pending" за повторно опитване при следващото стартиране на cron задачата.
                    </p>
                  </div>
                  <div class="col-md-4 text-right">
                    <button type="button" id="button-retry-failed-images" class="btn btn-warning btn-lg">
                      <i class="fa fa-refresh"></i> Рестартирай Failed изображения
                    </button>
                  </div>
                </div>

                <hr>

                <div class="row">
                  <div class="col-md-12">
                    <h5><i class="fa fa-info-circle"></i> Информация за Cron задачите:</h5>
                    <div class="table-responsive" id="cron-info-table">
                      <table class="table table-bordered table-condensed">
                        <thead>
                          <tr>
                            <th>Cron задача</th>
                            <th>Описание</th>
                            <th>Препоръчителна честота</th>
                          </tr>
                        </thead>
                        <tbody>
                          <tr>
                            <td><code>download_images.php</code></td>
                            <td>Изтегля pending изображения от опашката</td>
                            <td>На всеки 2 минути</td>
                          </tr>
                          <tr>
                            <td><code>retry_failed_images.php</code></td>
                            <td>Повторно опитване на failed изображения</td>
                            <td>Веднъж дневно (напр. в 02:00)</td>
                          </tr>
                        </tbody>
                      </table>
                    </div>
                  </div>
                </div>
              </div>
            </div>
          </div>

            <input type="hidden" id="is_developer" value="{{ is_developer }}">

          {# Условно добавяне на съдържанието на таб "Dev логове" #}
          {% if is_developer %}
          
          <div class="tab-pane" id="tab-dev-logs">
            <div class="pull-right"> {# Подравняване на бутона вдясно #}
                <button type="button" id="button-clear-dev-log" class="btn btn-danger btn-sm" data-toggle="tooltip" title="{{ button_clear_dev_log }}"><i class="fa fa-trash"></i> {{ button_clear_dev_log }}</button>
            </div>
            <h3 style="font-size: 18px; font-weight: bold;">{{ tab_dev_logs }}</h3>
            <div id="dev-log-content-area"> {# Добавяме ID на div-а, за да можем лесно да го изчистим #}
                {% if dev_log_content and dev_log_content|trim is not empty %}
                    <pre style="max-height: 600px; overflow-y: auto; white-space: pre-wrap; word-wrap: break-word; background-color: #f5f5f5; border: 1px solid #ccc; padding: 10px; border-radius: 4px;">{{ dev_log_content }}</pre>
                {% else %}
                    <p>{{ text_no_results }}</p> {# Показва "Няма резултати", ако dev_log_content е празен или съдържа само празни редове #}
                {% endif %}
            </div>
          </div>
          {% endif %}

        </div>
      </div>
    </div>
  </div>
<script type="text/javascript">
$(document).ready(function() {
    // === ОПРОСТЕНА ИНИЦИАЛИЗАЦИЯ НА ТАБОВЕ ===

    // Премахваме всички активни класове и скриваме всички табове
    $('.tab-content .tab-pane').removeClass('active').hide();
    $('.nav-tabs li').removeClass('active');

    // Активираме и показваме първия таб
    $('.nav-tabs li:first').addClass('active');
    $('.tab-content .tab-pane:first').addClass('active').show();

    // Проверка на статуса на връзката за всички изброени доставчици при зареждане на страницата
    $('.check-connection').each(function() {
        var button = $(this);
        var connector_id = button.data('connector-id');
        var status_cell = $('#status-' + connector_id);
        status_cell.html('<i class="fa fa-spinner fa-spin"></i>');

        $.ajax({
            url: 'index.php?route=extension/module/multi_feed_syncer/checkConnection&user_token={{ user_token }}&connector_id=' + connector_id,
            dataType: 'json',
            success: function(json) {
                if (json['status'] == 1) {
                    status_cell.html('<span class="label label-success" data-toggle="tooltip" title="' + json['message'] + '">✓</span>');
                } else {
                    status_cell.html('<span class="label label-danger" data-toggle="tooltip" title="' + json['message'] + '">✕</span>');
                }
                $('[data-toggle="tooltip"]').tooltip(); // Реинициализация на tooltip-овете
            },
            error: function(xhr, ajaxOptions, thrownError) {
                status_cell.html('<span class="label label-danger" data-toggle="tooltip" title="AJAX Грешка">✕</span>');
                console.log('AJAX Грешка: ' + thrownError + "\n" + xhr.responseText);
                $('[data-toggle="tooltip"]').tooltip();
            }
        });
    });

    // AJAX за бутона "Провери връзката"
    $(document).on('click', '.check-connection', function() {
        var button = $(this);
        var connector_id = button.data('connector-id');
        var status_cell = $('#status-' + connector_id);
        var original_html = status_cell.html();
        status_cell.html('<i class="fa fa-spinner fa-spin"></i>'); // Индикатор за зареждане
        button.prop('disabled', true);

        $.ajax({
            url: 'index.php?route=extension/module/multi_feed_syncer/checkConnection&user_token={{ user_token }}&connector_id=' + connector_id,
            dataType: 'json',
            success: function(json) {
                if (json['status'] == 1) {
                    status_cell.html('<span class="label label-success" data-toggle="tooltip" title="' + json['message'] + '">✓</span>');
                } else {
                    status_cell.html('<span class="label label-danger" data-toggle="tooltip" title="' + json['message'] + '">✕</span>');
                }
                 $('[data-toggle="tooltip"]').tooltip();
            },
            error: function(xhr, ajaxOptions, thrownError) {
                status_cell.html(original_html); // Връщане на оригиналния HTML при грешка
                alert('AJAX Грешка: ' + thrownError + "\n" + xhr.responseText);
            },
            complete: function() {
                button.prop('disabled', false);
            }
        });
    });



    // Активиране на конектор
    $(document).on('click', '.activate-connector', function() {
        var button = $(this);
        var connector_key = button.data('connector-key');
        button.prop('disabled', true).prepend('<i class="fa fa-spinner fa-spin"></i> ');

        $.ajax({
            url: 'index.php?route=extension/module/multi_feed_syncer/activateConnector&user_token={{ user_token }}',
            type: 'POST',
            dataType: 'json',
            data: { connector_key: connector_key },
            success: function(json) {
                if (json['success']) {
                    // Динамично добавяне към списъка с активни конектори
                    var new_row_html = '<tr id="supplier-row-' + json.new_connector_data.mfsc_id + '">' +
                    '<td class="text-left">' + json.new_connector_data.name + '</td>' +
                    '<td class="text-left">' + json.new_connector_data.connection_type + '</td>' +
                    '<td class="text-left" id="downloaded-data-' + json.new_connector_data.mfsc_id + '"><span class="text-muted">Няма данни</span></td>' +
                    '<td class="text-center" id="status-' + json.new_connector_data.mfsc_id + '"><span class="label label-default" title="' + '{{ text_status_unknown|escape('js') }}' +'">?</span></td>' +
                    '<td class="text-right">' +
                    '  <button type="button" class="btn btn-info btn-xs check-connection" data-connector-id="' + json.new_connector_data.mfsc_id + '" data-toggle="tooltip" title="' + '{{ button_check_connection|escape('js') }}' + '"><i class="fa fa-plug"></i></button>' +
                    '  <button type="button" class="btn btn-success btn-xs manual-sync-btn" ' +
                    '          data-mfsc-id="' + json.new_connector_data.mfsc_id + '" ' +
                    '          data-supplier-name="' + json.new_connector_data.name + '" ' +
                    '          data-toggle="tooltip" title="' + '{{ button_manual_sync|escape('js') }}' + '">' +
                    '      <i class="fa fa-refresh"></i> {{ button_manual_sync|escape('js') }}' +
                    '  </button>' +
                    '</td></tr>';

                    if ($('#activated-suppliers-list').find('td[colspan="5"]').length > 0) { // Ако има "Няма резултати"
                        $('#activated-suppliers-list').html(new_row_html);
                    } else {
                        $('#activated-suppliers-list').append(new_row_html);
                    }

                    $('#new-connector-row-' + connector_key).remove(); // Премахване от списъка с нови

                    // Задействане на проверка на връзката за новия ред
                    $('#supplier-row-' + json.new_connector_data.mfsc_id).find('.check-connection').trigger('click');
                    $('[data-toggle="tooltip"]').tooltip(); // Реинициализация


                    // Ако списъкът с нови конектори стане празен, скрий го
                    if ($('#new-connectors-list tr').length == 0) {
                        $('#new-connectors-list').closest('.table-responsive').prev('h4').remove();
                        $('#new-connectors-list').closest('.table-responsive').remove();
                    }

                    // Показване на съобщение за успех
                    $('#content .container-fluid:first > .alert-success, #content .container-fluid:first > .alert-danger').remove();
                    $('#content .container-fluid:first').prepend('<div class="alert alert-success alert-dismissible"><i class="fa fa-check-circle"></i> ' + json.success + '<button type="button" class="close" data-dismiss="alert">&times;</button></div>');

                }
                if (json['error']) {
                    // Показване на съобщение за грешка
                    $('#content .container-fluid:first > .alert-success, #content .container-fluid:first > .alert-danger').remove();
                    $('#content .container-fluid:first').prepend('<div class="alert alert-danger alert-dismissible"><i class="fa fa-exclamation-circle"></i> ' + json.error + '<button type="button" class="close" data-dismiss="alert">&times;</button></div>');
                    button.prop('disabled', false).find('.fa-spinner').remove();
                }
            },
            error: function(xhr, ajaxOptions, thrownError) {
                alert('AJAX Грешка: ' + thrownError + "\n" + xhr.responseText);
                button.prop('disabled', false).find('.fa-spinner').remove();
            }
        });
    });

    $('[data-toggle="tooltip"]').tooltip(); // Инициализация на tooltip-овете

    // Изчистване на Dev лога
    $(document).on('click', '#button-clear-dev-log', function() {
        // Използваме езиковата променлива за потвърждение
        if (confirm('{{ text_confirm_clear_dev_log }}')) {
            var $button = $(this);
            var original_button_html = $button.html(); // Запазваме оригиналния HTML на бутона
            $button.prop('disabled', true).html('<i class="fa fa-spinner fa-spin"></i> {{ text_processing|escape('js') }}'); // Използваме езикова променлива за "Обработка"

            $.ajax({
                url: 'index.php?route=extension/module/multi_feed_syncer/clearDevLog&user_token={{ user_token }}',
                type: 'POST', // POST е по-подходящ за действия, които променят състоянието на сървъра
                dataType: 'json',
                success: function(json) {
                    // Премахване на предишни съобщения за успех/грешка
                    $('#content .container-fluid:first > .alert-success, #content .container-fluid:first > .alert-danger').remove();

                    if (json['success']) {
                        // Изчистване на показаното съдържание на лога
                        $('#dev-log-content-area').html('<p>{{ text_no_results|escape('js') }}</p>'); 
                        // Показване на съобщение за успех
                        $('#content .container-fluid:first').prepend('<div class="alert alert-success alert-dismissible"><i class="fa fa-check-circle"></i> ' + json['success'] + '<button type="button" class="close" data-dismiss="alert">&times;</button></div>');
                    }
                    if (json['error']) {
                        // Показване на съобщение за грешка
                        $('#content .container-fluid:first').prepend('<div class="alert alert-danger alert-dismissible"><i class="fa fa-exclamation-circle"></i> ' + json['error'] + '<button type="button" class="close" data-dismiss="alert">&times;</button></div>');
                    }
                },
                error: function(xhr, ajaxOptions, thrownError) {
                    // Премахване на предишни съобщения за успех/грешка
                    $('#content .container-fluid:first > .alert-success, #content .container-fluid:first > .alert-danger').remove();
                    // Показване на AJAX грешка
                    $('#content .container-fluid:first').prepend('<div class="alert alert-danger alert-dismissible"><i class="fa fa-exclamation-circle"></i> ' + 'AJAX Грешка: ' + thrownError + '<button type="button" class="close" data-dismiss="alert">&times;</button></div>');
                    console.log('AJAX Грешка при изчистване на Dev лога: ' + thrownError + "\n" + xhr.responseText);
                },
                complete: function() {
                    // Връщане на оригиналния вид на бутона
                    $button.prop('disabled', false).html(original_button_html);
                }
            });
        }
    });

    // Обновяване на продуктите - показване на разширеното съдържание
    $(document).on('click', '.manual-sync-btn', function() {
        var $button = $(this);
        var mfsc_id = $button.data('mfsc-id');
        var supplier_name = $button.data('supplier-name');

        // Зареждаме разширеното съдържание вместо директно стартиране на синхронизацията
        loadExtendedContent(mfsc_id, supplier_name);
    });

    // Обработчик за бутона "Изтегли отново" в основната таблица
    $(document).on('click', '.download-file-again-btn', function() {
        var $button = $(this);
        var mfsc_id = $button.data('mfsc-id');
        var originalHtml = $button.html();

        $button.prop('disabled', true).html('<i class="fa fa-spinner fa-spin"></i> Изтегляне...');

        $.ajax({
            url: 'index.php?route=extension/module/multi_feed_syncer/downloadFileAgain&user_token={{ user_token }}',
            type: 'POST',
            data: { mfsc_id: mfsc_id },
            dataType: 'json',
            success: function(json) {
                if (json.success) {
                    showAlert('success', json.success);
                    // Обновяваме информацията в таблицата
                    updateDownloadedDataInTable(mfsc_id);
                } else {
                    showAlert('danger', json.error || 'Грешка при изтегляне на файла');
                }
            },
            error: function() {
                showAlert('danger', 'AJAX грешка при изтегляне на файла');
            },
            complete: function() {
                $button.prop('disabled', false).html(originalHtml);
            }
        });
    });

    // Стария код за директна синхронизация (запазваме за съвместимост)
    function startDirectSync(mfsc_id, supplier_name, $button) {
        var originalHtml = $button.html();

        if (!confirm('Сигурни ли сте, че искате да стартирате обновяване на продуктите за доставчик "' + supplier_name + '"?\n\nТова може да отнеме няколко минути.')) {
            return;
        }

        $button.prop('disabled', true).html('<i class="fa fa-spinner fa-spin"></i> Обработка...');

        $.ajax({
            url: 'index.php?route=extension/module/multi_feed_syncer/manualSync&user_token={{ user_token }}',
            type: 'POST',
            data: {
                mfsc_id: mfsc_id
            },
            dataType: 'json',
            success: function(json) {
                // Премахване на предишни съобщения
                $('#content .container-fluid:first > .alert-success, #content .container-fluid:first > .alert-danger').remove();

                if (json['success']) {
                    var statsMessage = '';
                    if (json['stats']) {
                        statsMessage = ' (Добавени: ' + json.stats.added +
                                     ', Актуализирани: ' + json.stats.updated +
                                     ', Пропуснати: ' + json.stats.skipped +
                                     ', Грешки: ' + json.stats.errors +
                                     ', Време: ' + json.stats.execution_time + ' сек.)';
                    }

                    $('#content .container-fluid:first').prepend(
                        '<div class="alert alert-success alert-dismissible">' +
                        '<i class="fa fa-check-circle"></i> ' + json['success'] + statsMessage +
                        '<button type="button" class="close" data-dismiss="alert">&times;</button>' +
                        '</div>'
                    );
                }
                if (json['error']) {
                    $('#content .container-fluid:first').prepend(
                        '<div class="alert alert-danger alert-dismissible">' +
                        '<i class="fa fa-exclamation-circle"></i> ' + json['error'] +
                        '<button type="button" class="close" data-dismiss="alert">&times;</button>' +
                        '</div>'
                    );
                }
            },
            error: function(xhr, ajaxOptions, thrownError) {
                // Премахване на предишни съобщения
                $('#content .container-fluid:first > .alert-success, #content .container-fluid:first > .alert-danger').remove();

                $('#content .container-fluid:first').prepend(
                    '<div class="alert alert-danger alert-dismissible">' +
                    '<i class="fa fa-exclamation-circle"></i> AJAX Грешка: ' + thrownError +
                    '<button type="button" class="close" data-dismiss="alert">&times;</button>' +
                    '</div>'
                );
                console.log('AJAX Грешка при обновяване: ' + thrownError + "\n" + xhr.responseText);
            },
            complete: function() {
                $button.prop('disabled', false).html(originalHtml);
            }
        });
    }



    // === ФУНКЦИОНАЛНОСТ ЗА УПРАВЛЕНИЕ НА ИЗОБРАЖЕНИЯ ===

    // Функция за зареждане на статистика за изображенията
    function loadImageStats() {
        $('#image-stats-content').html('<div class="text-center"><i class="fa fa-spinner fa-spin fa-2x"></i><p>Зареждане на статистика...</p></div>');

        $.ajax({
            url: 'index.php?route=extension/module/multi_feed_syncer/getImageQueueStats&user_token={{ user_token }}',
            type: 'GET',
            dataType: 'json',
            success: function(json) {
                if (json.success && json.stats) {
                    var statsHtml = '<div class="row">';

                    // Статистика по статуси
                    var statusLabels = {
                        'pending': { label: 'Чакащи', class: 'info', icon: 'clock-o' },
                        'processing': { label: 'Обработват се', class: 'warning', icon: 'spinner' },
                        'completed': { label: 'Завършени', class: 'success', icon: 'check' },
                        'failed': { label: 'Неуспешни', class: 'danger', icon: 'times' }
                    };

                    for (var status in statusLabels) {
                        var count = json.stats[status] ? json.stats[status].count : 0;
                        var avgAttempts = json.stats[status] ? json.stats[status].avg_attempts : 0;

                        statsHtml += '<div class="col-md-3">';
                        statsHtml += '<div class="panel panel-' + statusLabels[status].class + '" style="min-height: 180px;">';
                        statsHtml += '<div class="panel-heading text-center">';
                        statsHtml += '<i class="fa fa-' + statusLabels[status].icon + ' fa-2x"></i>';
                        statsHtml += '<h4>' + statusLabels[status].label + '</h4>';
                        statsHtml += '</div>';
                        statsHtml += '<div class="panel-body text-center">';
                        statsHtml += '<h3>' + count + '</h3>';
                        if (avgAttempts > 0) {
                            statsHtml += '<small>Средно опити: ' + avgAttempts + '</small>';
                        }
                        statsHtml += '</div>';
                        statsHtml += '</div>';
                        statsHtml += '</div>';
                    }

                    statsHtml += '</div>';
                    statsHtml += '<div class="text-center"><strong>Общо изображения в опашката: ' + json.total + '</strong></div>';

                    $('#image-stats-content').html(statsHtml);
                } else {
                    $('#image-stats-content').html('<div class="alert alert-warning"><i class="fa fa-exclamation-triangle"></i> Няма данни за статистика.</div>');
                }
            },
            error: function(xhr, ajaxOptions, thrownError) {
                $('#image-stats-content').html('<div class="alert alert-danger"><i class="fa fa-exclamation-circle"></i> Грешка при зареждане на статистика: ' + thrownError + '</div>');
                console.log('AJAX Грешка при зареждане на статистика: ' + thrownError + "\n" + xhr.responseText);
            }
        });
    }

    loadImageStats();

    // Бутон за обновяване на статистика
    $(document).on('click', '#button-refresh-stats', function() {
        var $button = $(this);
        var originalHtml = $button.html();

        $button.prop('disabled', true).html('<i class="fa fa-spinner fa-spin"></i> Зареждане...');

        loadImageStats();

        setTimeout(function() {
            $button.prop('disabled', false).html(originalHtml);
        }, 1000);
    });

    // Бутон за рестартиране на failed изображения
    $(document).on('click', '#button-retry-failed-images', function() {
        if (!confirm('Сигурни ли сте, че искате да рестартирате всички failed изображения?\n\nТова ще върне всички неуспешни изображения (с attempts < 5) в статус "pending" за повторно опитване.')) {
            return;
        }

        var $button = $(this);
        var originalHtml = $button.html();

        $button.prop('disabled', true).html('<i class="fa fa-spinner fa-spin"></i> Рестартиране...');

        $.ajax({
            url: 'index.php?route=extension/module/multi_feed_syncer/manualRetryFailedImages&user_token={{ user_token }}',
            type: 'POST',
            dataType: 'json',
            success: function(json) {
                // Премахване на предишни съобщения
                $('#content .container-fluid:first > .alert-success, #content .container-fluid:first > .alert-danger').remove();

                if (json.success) {
                    // Показване на съобщение за успех
                    $('#content .container-fluid:first').prepend(
                        '<div class="alert alert-success alert-dismissible">' +
                        '<i class="fa fa-check-circle"></i> ' + json.success +
                        '<button type="button" class="close" data-dismiss="alert">&times;</button>' +
                        '</div>'
                    );

                    // Обновяване на статистиката
                    loadImageStats();

                } else if (json.error) {
                    // Показване на съобщение за грешка
                    $('#content .container-fluid:first').prepend(
                        '<div class="alert alert-danger alert-dismissible">' +
                        '<i class="fa fa-exclamation-circle"></i> ' + json.error +
                        '<button type="button" class="close" data-dismiss="alert">&times;</button>' +
                        '</div>'
                    );
                }
            },
            error: function(xhr, ajaxOptions, thrownError) {
                // Премахване на предишни съобщения
                $('#content .container-fluid:first > .alert-success, #content .container-fluid:first > .alert-danger').remove();

                // Показване на AJAX грешка
                $('#content .container-fluid:first').prepend(
                    '<div class="alert alert-danger alert-dismissible">' +
                    '<i class="fa fa-exclamation-circle"></i> AJAX Грешка: ' + thrownError +
                    '<button type="button" class="close" data-dismiss="alert">&times;</button>' +
                    '</div>'
                );

                console.log('AJAX Грешка при рестартиране на failed изображения: ' + thrownError + "\n" + xhr.responseText);
            },
            complete: function() {
                // Връщане на оригиналния вид на бутона
                $button.prop('disabled', false).html(originalHtml);
            }
        });
    });

    

    // === ФУНКЦИОНАЛНОСТ ЗА РАЗШИРЕНО СЪДЪРЖАНИЕ ===

    // Функция за зареждане на разширеното съдържание
    function loadExtendedContent(mfsc_id, supplier_name) {
        var $content = $('#suppliers-extended-content');

        // Показваме индикатор за зареждане
        $content.html('<div class="text-center"><i class="fa fa-spinner fa-spin fa-2x"></i><p>Зареждане...</p></div>');

        // Зареждаме само информацията за файла
        loadFileInfo(mfsc_id).then(function(fileInfo) {
            // Генерираме HTML съдържанието без категориите
            var html = generateExtendedContentHTML(mfsc_id, supplier_name, fileInfo, null);
            $content.html(html);

            // Инициализираме функционалността
            initializeExtendedContentHandlers(mfsc_id);

        }).catch(function(error) {
            $content.html('<div class="alert alert-danger"><i class="fa fa-exclamation-circle"></i> Грешка при зареждане: ' + error + '</div>');
        });
    }

    // Функция за зареждане на информацията за файла
    function loadFileInfo(mfsc_id) {
        return new Promise(function(resolve, reject) {
            $.ajax({
                url: 'index.php?route=extension/module/multi_feed_syncer/getLastFileDownloadInfo&user_token={{ user_token }}&mfsc_id=' + mfsc_id,
                type: 'GET',
                dataType: 'json',
                success: function(json) {
                    if (json.success) {
                        resolve(json.file_info);
                    } else {
                        reject(json.error || 'Неизвестна грешка');
                    }
                },
                error: function() {
                    reject('AJAX грешка при зареждане на информация за файла');
                }
            });
        });
    }

    // Функция за зареждане на съответствията на категориите
    function loadCategoriesMapping(mfsc_id) {
        return new Promise(function(resolve, reject) {
            $.ajax({
                url: 'index.php?route=extension/module/multi_feed_syncer/getCategoriesMapping&user_token={{ user_token }}&mfsc_id=' + mfsc_id,
                type: 'GET',
                dataType: 'json',
                success: function(json) {
                    if (json.success) {
                        resolve(json.categories);
                    } else {
                        reject(json.error || 'Неизвестна грешка');
                    }
                },
                error: function() {
                    reject('AJAX грешка при зареждане на категории');
                }
            });
        });
    }

    // Функция за зареждане само на таблицата със съответствията
    function loadCategoriesMappingTable(mfsc_id) {
        return new Promise(function(resolve, reject) {
            $.ajax({
                url: 'index.php?route=extension/module/multi_feed_syncer/loadCategoriesMappingTable&user_token={{ user_token }}&mfsc_id=' + mfsc_id,
                type: 'GET',
                dataType: 'json',
                success: function(json) {
                    if (json.success) {
                        resolve(json.categories);
                    } else {
                        reject(json.error || 'Неизвестна грешка');
                    }
                },
                error: function() {
                    reject('AJAX грешка при зареждане на категории');
                }
            });
        });
    }

    // Функция за попълване на таблицата с категории
    function populateCategoriesTable(categoriesData) {
        var $tbody = $('#categories-mapping-tbody');
        $tbody.empty();

        if (categoriesData && categoriesData.length > 0) {
            categoriesData.forEach(function(category, index) {
                var rowClass = category.is_new ? 'warning' : '';
                var autoSuggestedClass = category.auto_suggested ? 'info' : '';
                if (autoSuggestedClass) rowClass += ' ' + autoSuggestedClass;

                var html = '<tr class="' + rowClass + '" data-source-path="' + category.source_path + '" data-suggested-name="' + (category.suggested_category_name || '') + '">';
                html += '<td>' + category.source_path;
                if (category.is_new) {
                    html += ' <span class="label label-warning">Нова</span>';
                }
                if (category.auto_suggested) {
                    html += ' <span class="label label-info">Автоматично предложена</span>';
                }
                html += '</td>';
                html += '<td class="target-category-cell">';
                html += '<span class="target-path-display">' + (category.target_path || '') + '</span>';
                html += '<div class="target-path-edit" style="display: none;">';
                html += '<input type="text" class="form-control target-path-input" value="' + (category.target_path || '') + '" placeholder="{{ text_search_categories }}">';
                html += '<div class="suggestions-dropdown" style="display: none;"></div>';
                html += '</div>';

                // Форма за създаване на нова категория
                html += '<div class="new-category-form" style="display: none; margin-top: 10px; padding: 10px; border: 1px solid #ddd; border-radius: 4px; background-color: #f9f9f9;">';
                html += '<h5><i class="fa fa-plus"></i> Създаване на нова категория</h5>';
                html += '<div class="form-group">';
                html += '<label>Родителска категория:</label>';
                html += '<input type="text" class="form-control parent-category-input" placeholder="Търсете родителска категория...">';
                html += '<input type="hidden" class="parent-category-id" value="">';
                html += '<div class="parent-suggestions-dropdown" style="display: none;"></div>';
                html += '</div>';
                html += '<div class="form-group">';
                html += '<label>Име на новата категория:</label>';
                html += '<input type="text" class="form-control new-category-name" value="' + (category.suggested_category_name || '') + '">';
                html += '</div>';
                html += '<div class="form-group">';
                html += '<button type="button" class="btn btn-xs btn-success save-new-category-btn">Запази нова категория</button>';
                html += '<button type="button" class="btn btn-xs btn-default cancel-new-category-btn">Отказ</button>';
                html += '</div>';
                html += '</div>';

                html += '</td>';
                html += '<td>';

                // Показваме различни бутони в зависимост от състоянието
                if (!category.target_path || category.target_path === '') {
                    html += '<button type="button" class="btn btn-xs btn-success create-new-category-btn">Създай нова категория</button><br style="margin-bottom: 5px;">';
                }
                html += '<button type="button" class="btn btn-xs btn-primary edit-mapping-btn">Редактирай</button>';
                html += '<button type="button" class="btn btn-xs btn-success save-mapping-btn" style="display: none;">Запази</button>';
                html += '<button type="button" class="btn btn-xs btn-default cancel-mapping-btn" style="display: none;">Отказ</button>';
                html += '</td>';
                html += '</tr>';

                $tbody.append(html);
            });
        } else {
            $tbody.html('<tr><td colspan="3" class="text-center">{{ text_no_categories_found }}</td></tr>');
        }
    }

    // Функция за генериране на HTML съдържанието
    function generateExtendedContentHTML(mfsc_id, supplier_name, fileInfo, categoriesData) {
        var html = '<div class="panel panel-default">';
        html += '<div class="panel-heading"><h3 class="panel-title">Обновяване на продуктите от доставчик "' + supplier_name + '"</h3></div>';
        html += '<div class="panel-body">';

        html += '<hr>';

        // Секция за съответствията на категориите
        html += '<div class="row">';
        html += '<div class="col-md-12">';
        html += '<h4 style="text-align: center; font-weight: bold;">{{ text_categories_mapping }}</h4>';
        html += '<div class="text-center" style="margin-bottom: 20px;">';
        html += '<button type="button" class="btn btn-info btn-lg" id="btn-view-mappings" data-mfsc-id="' + mfsc_id + '">';
        html += '<i class="fa fa-eye"></i> {{ button_view_mappings }}</button>';
        html += '</div>';
        html += '<div id="categories-mapping-container" style="display: none;">';
        html += '<div class="table-responsive">';
        html += '<table class="table table-bordered" id="categories-mapping-table">';
        html += '<thead>';
        html += '<tr>';
        html += '<th>{{ text_source_category }}</th>';
        html += '<th>{{ text_target_category }}</th>';
        html += '<th style="width: 100px;">Действие</th>';
        html += '</tr>';
        html += '</thead>';
        html += '<tbody id="categories-mapping-tbody">';

        // Таблицата ще се попълни динамично при натискане на бутона "Виж съответствията"
        html += '<tr><td colspan="3" class="text-center">Натиснете "{{ button_view_mappings }}" за да видите съответствията</td></tr>';

        html += '</tbody>';
        html += '</table>';
        html += '</div>';

        html += '<div class="text-right" style="margin-top: 15px; display: none;" id="save-mappings-container">';
        html += '<button type="button" class="btn btn-success" id="btn-save-all-mappings" data-mfsc-id="' + mfsc_id + '">';
        html += '<i class="fa fa-save"></i> {{ button_save_mappings }}</button>';
        html += '</div>';

        html += '</div>'; // Затваряме categories-mapping-container

        html += '</div>';
        html += '</div>';

        html += '<hr style="border-top: 2px solid #ddd;">';

        // Бутон за стартиране на синхронизация
        html += '<div class="text-center">';
        html += '<button type="button" class="btn btn-success btn-lg" id="btn-start-products-update" data-mfsc-id="' + mfsc_id + '" data-supplier-name="' + supplier_name + '">';
        html += '<i class="fa fa-refresh"></i> {{ button_start_update }}</button>';
        html += '</div>';

        html += '</div>';
        html += '</div>';

        return html;
    }

    // Функция за инициализиране на обработчиците на събитията
    function initializeExtendedContentHandlers(mfsc_id) {
        // Обработчик за показване на съответствията на категориите
        $(document).on('click', '#btn-view-mappings', function() {
            var $button = $(this);
            var originalHtml = $button.html();

            $button.prop('disabled', true).html('<i class="fa fa-spinner fa-spin"></i> Зареждане...');

            loadCategoriesMappingTable(mfsc_id).then(function(categoriesData) {
                // Показваме контейнера с таблицата
                $('#categories-mapping-container').show();
                $('#save-mappings-container').show();

                // Попълваме таблицата с данни
                populateCategoriesTable(categoriesData);

                // Скриваме бутона "Виж съответствията"
                $button.hide();

            }).catch(function(error) {
                showAlert('danger', 'Грешка при зареждане на категории: ' + error);
                $button.prop('disabled', false).html(originalHtml);
            });
        });

        // Обработчик за изтегляне на файла отново
        $(document).on('click', '#btn-download-file-again', function() {
            var $button = $(this);
            var originalHtml = $button.html();

            $button.prop('disabled', true).html('<i class="fa fa-spinner fa-spin"></i> Изтегляне...');

            $.ajax({
                url: 'index.php?route=extension/module/multi_feed_syncer/downloadFileAgain&user_token={{ user_token }}',
                type: 'POST',
                data: { mfsc_id: mfsc_id },
                dataType: 'json',
                success: function(json) {
                    if (json.success) {
                        showAlert('success', json.success);
                        // Презареждаме информацията за файла
                        loadFileInfo(mfsc_id).then(function(fileInfo) {
                            updateFileInfoDisplay(fileInfo);
                        });
                    } else {
                        showAlert('danger', json.error || 'Грешка при изтегляне на файла');
                    }
                },
                error: function() {
                    showAlert('danger', 'AJAX грешка при изтегляне на файла');
                },
                complete: function() {
                    $button.prop('disabled', false).html(originalHtml);
                }
            });
        });

        // Обработчик за редактиране на съответствие
        $(document).on('click', '.edit-mapping-btn', function() {
            var $row = $(this).closest('tr');
            var $displaySpan = $row.find('.target-path-display');
            var $editDiv = $row.find('.target-path-edit');
            var $editBtn = $(this);
            var $saveBtn = $row.find('.save-mapping-btn');
            var $cancelBtn = $row.find('.cancel-mapping-btn');

            $displaySpan.hide();
            $editDiv.show();
            $editBtn.hide();
            $saveBtn.show();
            $cancelBtn.show();

            // Фокусираме полето за въвеждане
            $row.find('.target-path-input').focus();
        });

        // Обработчик за отказ на редактиране
        $(document).on('click', '.cancel-mapping-btn', function() {
            var $row = $(this).closest('tr');
            cancelEditMapping($row);
        });

        // Обработчик за запазване на единично съответствие
        $(document).on('click', '.save-mapping-btn', function() {
            var $row = $(this).closest('tr');
            var newTargetPath = $row.find('.target-path-input').val();

            // Актуализираме дисплея
            $row.find('.target-path-display').text(newTargetPath);
            cancelEditMapping($row);
        });

        // Автозавършване за търсене на категории
        $(document).on('input', '.target-path-input', function() {
            var $input = $(this);
            var searchTerm = $input.val();
            var $dropdown = $input.siblings('.suggestions-dropdown');

            if (searchTerm.length >= 2) {
                $.ajax({
                    url: 'index.php?route=extension/module/multi_feed_syncer/searchCategories&user_token={{ user_token }}&search=' + encodeURIComponent(searchTerm),
                    type: 'GET',
                    dataType: 'json',
                    success: function(json) {
                        if (json.success && json.suggestions.length > 0) {
                            var suggestionsHtml = '';
                            json.suggestions.forEach(function(suggestion) {
                                suggestionsHtml += '<div class="suggestion-item" data-path="' + suggestion.path + '">' + suggestion.path + '</div>';
                            });
                            $dropdown.html(suggestionsHtml).show();
                        } else {
                            $dropdown.hide();
                        }
                    },
                    error: function() {
                        $dropdown.hide();
                    }
                });
            } else {
                $dropdown.hide();
            }
        });

        // Обработчик за избор на предложение
        $(document).on('click', '.suggestion-item', function() {
            var $item = $(this);
            var path = $item.data('path');
            var $input = $item.closest('.target-path-edit').find('.target-path-input');
            var $dropdown = $item.closest('.suggestions-dropdown');

            $input.val(path);
            $dropdown.hide();
        });

        // Скриване на предложенията при клик извън тях
        $(document).on('click', function(e) {
            if (!$(e.target).closest('.target-path-edit').length) {
                $('.suggestions-dropdown').hide();
            }
            if (!$(e.target).closest('.new-category-form').length) {
                $('.parent-suggestions-dropdown').hide();
            }
        });

        // Обработчик за показване на формата за създаване на нова категория
        $(document).on('click', '.create-new-category-btn', function() {
            var $row = $(this).closest('tr');
            var $form = $row.find('.new-category-form');
            var $targetCell = $row.find('.target-category-cell');

            // Скриваме другите елементи и показваме формата
            $targetCell.find('.target-path-display, .target-path-edit').hide();
            $form.show();

            // Фокусираме полето за родителска категория
            $form.find('.parent-category-input').focus();
        });

        // Обработчик за отказ на създаване на нова категория
        $(document).on('click', '.cancel-new-category-btn', function() {
            var $row = $(this).closest('tr');
            cancelNewCategoryForm($row);
        });

        // Обработчик за запазване на нова категория
        $(document).on('click', '.save-new-category-btn', function() {
            var $row = $(this).closest('tr');
            var $form = $row.find('.new-category-form');
            var parentId = $form.find('.parent-category-id').val();
            var categoryName = $form.find('.new-category-name').val().trim();

            if (!categoryName) {
                showAlert('warning', 'Моля, въведете име на категорията');
                return;
            }

            // Маркираме реда за създаване на нова категория
            $row.data('create-new-category', true);
            $row.data('new-category-name', categoryName);
            $row.data('new-category-parent-id', parentId || 0);

            // Показваме информацията в target-path-display
            var parentPath = $form.find('.parent-category-input').val();
            var fullPath = parentPath ? parentPath + ' > ' + categoryName : categoryName;
            $row.find('.target-path-display').text(fullPath + ' (нова категория)').css('font-style', 'italic').css('color', '#337ab7');

            // Скриваме формата и показваме дисплея
            cancelNewCategoryForm($row);

            showAlert('info', 'Категорията "' + fullPath + '" ще бъде създадена при запазване');
        });

        // Автозавършване за родителска категория
        $(document).on('input', '.parent-category-input', function() {
            var $input = $(this);
            var searchTerm = $input.val();
            var $dropdown = $input.siblings('.parent-suggestions-dropdown');

            if (searchTerm.length >= 2) {
                $.ajax({
                    url: 'index.php?route=extension/module/multi_feed_syncer/searchCategories&user_token={{ user_token }}&search=' + encodeURIComponent(searchTerm),
                    type: 'GET',
                    dataType: 'json',
                    success: function(json) {
                        if (json.success && json.suggestions.length > 0) {
                            var suggestionsHtml = '';
                            json.suggestions.forEach(function(suggestion) {
                                suggestionsHtml += '<div class="parent-suggestion-item" data-path="' + suggestion.path + '" data-id="' + suggestion.id + '">' + suggestion.path + '</div>';
                            });
                            $dropdown.html(suggestionsHtml).show();
                        } else {
                            $dropdown.hide();
                        }
                    },
                    error: function() {
                        $dropdown.hide();
                    }
                });
            } else {
                $dropdown.hide();
            }
        });

        // Обработчик за избор на родителска категория
        $(document).on('click', '.parent-suggestion-item', function() {
            var $item = $(this);
            var path = $item.data('path');
            var id = $item.data('id');
            var $input = $item.closest('.new-category-form').find('.parent-category-input');
            var $hiddenInput = $item.closest('.new-category-form').find('.parent-category-id');
            var $dropdown = $item.closest('.parent-suggestions-dropdown');

            $input.val(path);
            $hiddenInput.val(id);
            $dropdown.hide();
        });

        // Обработчик за запазване на всички съответствия
        $(document).on('click', '#btn-save-all-mappings', function() {
            var $button = $(this);
            var originalHtml = $button.html();
            var mappings = [];

            // Събираме всички съответствия
            $('#categories-mapping-table tbody tr').each(function() {
                var $row = $(this);
                var sourcePath = $row.data('source-path');
                var targetPath = $row.find('.target-path-display').text().trim();

                // Проверяваме дали е маркирано за създаване на нова категория
                var createNewCategory = $row.data('create-new-category');
                var newCategoryName = $row.data('new-category-name');
                var newCategoryParentId = $row.data('new-category-parent-id');

                if (sourcePath) {
                    var mapping = {
                        source_path: sourcePath
                    };

                    if (createNewCategory && newCategoryName) {
                        // Добавяме данни за създаване на нова категория
                        mapping.create_new_category = true;
                        mapping.new_category_name = newCategoryName;
                        mapping.new_category_parent_id = newCategoryParentId || 0;
                    } else if (targetPath && !targetPath.includes('(нова категория)')) {
                        // Добавяме съществуващия target_path само ако не е маркиран като нова категория
                        mapping.target_path = targetPath;
                    }

                    // Добавяме mapping-а само ако има target_path или е маркиран за създаване на нова категория
                    if (mapping.target_path || mapping.create_new_category) {
                        mappings.push(mapping);
                    }
                }
            });

            if (mappings.length === 0) {
                showAlert('warning', 'Няма съответствия за запазване');
                return;
            }

            $button.prop('disabled', true).html('<i class="fa fa-spinner fa-spin"></i> Запазване...');

            $.ajax({
                url: 'index.php?route=extension/module/multi_feed_syncer/saveCategoriesMapping&user_token={{ user_token }}',
                type: 'POST',
                data: {
                    mfsc_id: mfsc_id,
                    mappings: mappings
                },
                dataType: 'json',
                success: function(json) {
                    if (json.success) {
                        var message = json.success;

                        // Ако има създадени категории, показваме допълнителна информация
                        if (json.created_categories && json.created_categories.length > 0) {
                            message += '<br><strong>Създадени категории:</strong><ul>';
                            json.created_categories.forEach(function(cat) {
                                message += '<li>' + cat.name + ' (Път: ' + cat.path + ')</li>';
                            });
                            message += '</ul>';
                        }

                        showAlert('success', message);

                        // Премахваме маркировката за нови категории и актуализираме дисплея
                        $('#categories-mapping-table tbody tr').each(function() {
                            var $row = $(this);
                            $row.removeClass('warning info');
                            $row.find('.label-warning, .label-info').remove();

                            // Ако е била създадена нова категория, актуализираме дисплея
                            if ($row.data('create-new-category')) {
                                var $display = $row.find('.target-path-display');
                                var text = $display.text().replace(' (нова категория)', '');
                                $display.text(text).css('font-style', 'normal').css('color', '');

                                // Премахваме данните за нова категория
                                $row.removeData('create-new-category');
                                $row.removeData('new-category-name');
                                $row.removeData('new-category-parent-id');
                            }
                        });
                    } else {
                        showAlert('danger', json.error || 'Грешка при запазване');
                    }
                },
                error: function() {
                    showAlert('danger', 'AJAX грешка при запазване на съответствията');
                },
                complete: function() {
                    $button.prop('disabled', false).html(originalHtml);
                }
            });
        });

        // Обработчик за стартиране на обновяване на продуктите
        $(document).on('click', '#btn-start-products-update', function() {
            var $button = $(this);
            var supplierName = $button.data('supplier-name');
            var originalHtml = $button.html();

            if (!confirm('Сигурни ли сте, че искате да стартирате обновяване на продуктите за "' + supplierName + '"?\n\nТова може да отнеме няколко минути.')) {
                return;
            }

            $button.prop('disabled', true).html('<i class="fa fa-spinner fa-spin"></i> Обновяване...');

            $.ajax({
                url: 'index.php?route=extension/module/multi_feed_syncer/startProductsUpdate&user_token={{ user_token }}',
                type: 'POST',
                data: { mfsc_id: mfsc_id },
                dataType: 'json',
                success: function(json) {
                    if (json.success) {
                        var statsMessage = '';
                        if (json.stats) {
                            statsMessage = ' (Добавени: ' + json.stats.added +
                                         ', Актуализирани: ' + json.stats.updated +
                                         ', Пропуснати: ' + json.stats.skipped +
                                         ', Грешки: ' + json.stats.errors +
                                         ', Време: ' + json.stats.execution_time + ' сек.)';
                        }
                        showAlert('success', json.success + statsMessage);
                    } else {
                        showAlert('danger', json.error || 'Грешка при обновяване на продуктите');
                    }
                },
                error: function() {
                    showAlert('danger', 'AJAX грешка при стартиране на обновяването');
                },
                complete: function() {
                    $button.prop('disabled', false).html(originalHtml);
                }
            });
        });
    }

    // Функция за обновяване на информацията за изтеглените данни в таблицата
    function updateDownloadedDataInTable(mfsc_id) {
        $.ajax({
            url: 'index.php?route=extension/module/multi_feed_syncer/getLastFileDownloadInfo&user_token={{ user_token }}&mfsc_id=' + mfsc_id,
            type: 'GET',
            dataType: 'json',
            success: function(json) {
                if (json.success && json.file_info) {
                    var fileInfo = json.file_info;
                    var $cell = $('#downloaded-data-' + mfsc_id);

                    if (fileInfo.file_exists) {
                        var html = fileInfo.last_modified + '<br><small>' + fileInfo.file_size + '</small>';
                        html += '<br><button type="button" class="btn btn-primary btn-xs download-file-again-btn" ';
                        html += 'data-mfsc-id="' + mfsc_id + '" ';
                        html += 'data-toggle="tooltip" title="{{ button_download_file_again }}" ';
                        html += 'style="margin-top: 5px;">';
                        html += '<i class="fa fa-download"></i> Изтегли отново</button>';
                        $cell.html(html);
                    } else {
                        $cell.html('<span class="text-muted">Няма данни</span>');
                    }

                    // Реинициализираме tooltip-овете
                    $('[data-toggle="tooltip"]').tooltip();
                }
            },
            error: function() {
                console.log('Грешка при обновяване на информацията за файла');
            }
        });
    }

    // Помощни функции
    function cancelEditMapping($row) {
        var $displaySpan = $row.find('.target-path-display');
        var $editDiv = $row.find('.target-path-edit');
        var $editBtn = $row.find('.edit-mapping-btn');
        var $saveBtn = $row.find('.save-mapping-btn');
        var $cancelBtn = $row.find('.cancel-mapping-btn');

        $editDiv.hide();
        $displaySpan.show();
        $saveBtn.hide();
        $cancelBtn.hide();
        $editBtn.show();

        // Скриваме предложенията
        $row.find('.suggestions-dropdown').hide();
    }

    function cancelNewCategoryForm($row) {
        var $form = $row.find('.new-category-form');
        var $targetCell = $row.find('.target-category-cell');

        // Скриваме формата
        $form.hide();

        // Показваме обратно target-path-display
        $targetCell.find('.target-path-display').show();

        // Скриваме предложенията
        $form.find('.parent-suggestions-dropdown').hide();

        // Изчистваме полетата
        $form.find('.parent-category-input').val('');
        $form.find('.parent-category-id').val('');
        $form.find('.new-category-name').val($row.data('suggested-name') || '');
    }

    function updateFileInfoDisplay(fileInfo) {
        // Тази функция може да се използва за актуализиране на дисплея на информацията за файла
        // след успешно изтегляне
    }

    function showAlert(type, message) {
        // Премахваме предишни съобщения
        $('#content .container-fluid:first > .alert-success, #content .container-fluid:first > .alert-danger, #content .container-fluid:first > .alert-warning').remove();

        var alertClass = 'alert-' + type;
        var iconClass = type === 'success' ? 'fa-check-circle' : (type === 'warning' ? 'fa-exclamation-triangle' : 'fa-exclamation-circle');

        var alertHtml = '<div class="alert ' + alertClass + ' alert-dismissible">' +
                       '<i class="fa ' + iconClass + '"></i> ' + message +
                       '<button type="button" class="close" data-dismiss="alert">&times;</button>' +
                       '</div>';

        $('#content .container-fluid:first').prepend(alertHtml);
    }


});


</script>
</div>
{{ footer }}